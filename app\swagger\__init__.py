from flask_restx import Api
from flask import Blueprint

# 创建API蓝图
api_bp = Blueprint('api', __name__, url_prefix='/api/v1')

# 创建API实例
api = Api(
    api_bp,
    version='1.0',
    title='AI医疗小助手API',
    description='AI医疗小助手后端API文档',
    doc='/docs/',  # Swagger UI路径
    contact='开发团队',
    contact_email='<EMAIL>',
    authorizations={
        'Bearer': {
            'type': 'apiKey',
            'in': 'header',
            'name': 'Authorization',
            'description': 'JWT Token认证，格式：Bearer <token>'
        }
    },
    security='Bearer'
)

# 导入命名空间
from app.swagger.auth_ns import auth_ns
from app.swagger.conversation_ns import conversation_ns

# 注册命名空间
api.add_namespace(auth_ns, path='/auth')
api.add_namespace(conversation_ns, path='/conversations')
