import os
from app import create_app, db
from app.models import User, Conversation, Message

# 创建应用实例
app = create_app(os.getenv('FLASK_ENV', 'development'))

@app.shell_context_processor
def make_shell_context():
    """为Flask shell提供上下文"""
    return {
        'db': db,
        'User': User,
        'Conversation': Conversation,
        'Message': Message
    }

@app.cli.command()
def init_db():
    """初始化数据库"""
    db.create_all()
    print('数据库初始化完成！')

@app.cli.command()
def create_admin():
    """创建管理员用户"""
    username = input('请输入管理员用户名: ')
    email = input('请输入管理员邮箱: ')
    password = input('请输入管理员密码: ')
    
    if User.query.filter_by(username=username).first():
        print('用户名已存在！')
        return
    
    if User.query.filter_by(email=email).first():
        print('邮箱已被注册！')
        return
    
    admin = User(username=username, email=email, password=password)
    db.session.add(admin)
    db.session.commit()
    print(f'管理员用户 {username} 创建成功！')

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000, debug=True)
