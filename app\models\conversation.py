from datetime import datetime
from app import db

class Conversation(db.Model):
    """对话模型"""
    __tablename__ = 'conversations'
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.<PERSON><PERSON>('users.id'), nullable=False)
    title = db.Column(db.String(200), nullable=False, default='新对话')
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关系
    messages = db.relationship('Message', backref='conversation', lazy='dynamic', 
                             cascade='all, delete-orphan', order_by='Message.created_at')
    
    def __init__(self, user_id, title='新对话'):
        self.user_id = user_id
        self.title = title
    
    def get_last_message(self):
        """获取最后一条消息"""
        return self.messages.order_by(db.desc('created_at')).first()
    
    def get_message_count(self):
        """获取消息数量"""
        return self.messages.count()
    
    def to_dict(self, include_messages=False):
        """转换为字典"""
        result = {
            'id': self.id,
            'user_id': self.user_id,
            'title': self.title,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'message_count': self.get_message_count()
        }
        
        if include_messages:
            result['messages'] = [msg.to_dict() for msg in self.messages]
        else:
            # 只包含最后一条消息的预览
            last_message = self.get_last_message()
            result['last_message'] = last_message.to_dict() if last_message else None
        
        return result
    
    def __repr__(self):
        return f'<Conversation {self.id}: {self.title}>'
