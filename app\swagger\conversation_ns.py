from flask_restx import Namespace, Resource, fields
from flask import request
from flask_jwt_extended import jwt_required, get_jwt_identity
from app import db
from app.models.user import User
from app.models.conversation import Conversation
from app.models.message import Message
from app.utils.ai_service import get_ai_response

# 创建对话命名空间
conversation_ns = Namespace('conversations', description='对话管理相关接口')

# 定义数据模型
conversation_create_model = conversation_ns.model('ConversationCreate', {
    'title': fields.String(description='对话标题', example='健康咨询')
})

conversation_update_model = conversation_ns.model('ConversationUpdate', {
    'title': fields.String(required=True, description='对话标题', example='更新后的标题')
})

message_create_model = conversation_ns.model('MessageCreate', {
    'content': fields.String(required=True, description='消息内容', example='我最近头痛，请问可能是什么原因？')
})

message_model = conversation_ns.model('Message', {
    'id': fields.Integer(description='消息ID'),
    'conversation_id': fields.Integer(description='对话ID'),
    'role': fields.String(description='角色', enum=['user', 'assistant']),
    'content': fields.String(description='消息内容'),
    'created_at': fields.String(description='创建时间')
})

conversation_model = conversation_ns.model('Conversation', {
    'id': fields.Integer(description='对话ID'),
    'user_id': fields.Integer(description='用户ID'),
    'title': fields.String(description='对话标题'),
    'created_at': fields.String(description='创建时间'),
    'updated_at': fields.String(description='更新时间'),
    'message_count': fields.Integer(description='消息数量'),
    'last_message': fields.Nested(message_model, description='最后一条消息', allow_null=True)
})

conversation_detail_model = conversation_ns.model('ConversationDetail', {
    'id': fields.Integer(description='对话ID'),
    'user_id': fields.Integer(description='用户ID'),
    'title': fields.String(description='对话标题'),
    'created_at': fields.String(description='创建时间'),
    'updated_at': fields.String(description='更新时间'),
    'message_count': fields.Integer(description='消息数量'),
    'messages': fields.List(fields.Nested(message_model), description='消息列表')
})

pagination_model = conversation_ns.model('Pagination', {
    'page': fields.Integer(description='当前页码'),
    'per_page': fields.Integer(description='每页数量'),
    'total': fields.Integer(description='总记录数'),
    'pages': fields.Integer(description='总页数'),
    'has_next': fields.Boolean(description='是否有下一页'),
    'has_prev': fields.Boolean(description='是否有上一页')
})

conversations_response_model = conversation_ns.model('ConversationsResponse', {
    'conversations': fields.List(fields.Nested(conversation_model), description='对话列表'),
    'pagination': fields.Nested(pagination_model, description='分页信息')
})

conversation_response_model = conversation_ns.model('ConversationResponse', {
    'message': fields.String(description='响应消息'),
    'conversation': fields.Nested(conversation_model, description='对话信息')
})

conversation_detail_response_model = conversation_ns.model('ConversationDetailResponse', {
    'conversation': fields.Nested(conversation_detail_model, description='对话详情')
})

messages_response_model = conversation_ns.model('MessagesResponse', {
    'messages': fields.List(fields.Nested(message_model), description='消息列表'),
    'pagination': fields.Nested(pagination_model, description='分页信息')
})

send_message_response_model = conversation_ns.model('SendMessageResponse', {
    'message': fields.String(description='响应消息'),
    'user_message': fields.Nested(message_model, description='用户消息'),
    'ai_message': fields.Nested(message_model, description='AI回复', allow_null=True),
    'error': fields.String(description='错误信息', allow_null=True)
})

error_model = conversation_ns.model('Error', {
    'message': fields.String(description='错误消息')
})

@conversation_ns.route('')
class ConversationList(Resource):
    @conversation_ns.doc('get_conversations', security='Bearer')
    @conversation_ns.param('page', '页码', type='integer', default=1)
    @conversation_ns.param('per_page', '每页数量', type='integer', default=20)
    @conversation_ns.marshal_with(conversations_response_model, code=200, description='获取成功')
    @conversation_ns.marshal_with(error_model, code=500, description='服务器内部错误')
    @jwt_required()
    def get(self):
        """获取用户的对话列表"""
        try:
            user_id = int(get_jwt_identity())
            page = request.args.get('page', 1, type=int)
            per_page = request.args.get('per_page', 20, type=int)
            
            # 限制每页数量
            per_page = min(per_page, 100)
            
            conversations = Conversation.query.filter_by(user_id=user_id)\
                .order_by(Conversation.updated_at.desc())\
                .paginate(page=page, per_page=per_page, error_out=False)
            
            return {
                'conversations': [conv.to_dict() for conv in conversations.items],
                'pagination': {
                    'page': page,
                    'per_page': per_page,
                    'total': conversations.total,
                    'pages': conversations.pages,
                    'has_next': conversations.has_next,
                    'has_prev': conversations.has_prev
                }
            }, 200
            
        except Exception as e:
            return {'message': '获取对话列表失败'}, 500

    @conversation_ns.doc('create_conversation', security='Bearer')
    @conversation_ns.expect(conversation_create_model)
    @conversation_ns.marshal_with(conversation_response_model, code=201, description='创建成功')
    @conversation_ns.marshal_with(error_model, code=500, description='服务器内部错误')
    @jwt_required()
    def post(self):
        """创建新对话"""
        try:
            user_id = int(get_jwt_identity())
            data = request.get_json() or {}
            
            title = data.get('title', '新对话').strip()
            if not title:
                title = '新对话'
            
            # 创建对话
            conversation = Conversation(user_id=user_id, title=title)
            db.session.add(conversation)
            db.session.commit()
            
            return {
                'message': '对话创建成功',
                'conversation': conversation.to_dict()
            }, 201
            
        except Exception as e:
            db.session.rollback()
            return {'message': '创建对话失败'}, 500

@conversation_ns.route('/<int:conversation_id>')
class ConversationDetail(Resource):
    @conversation_ns.doc('get_conversation', security='Bearer')
    @conversation_ns.marshal_with(conversation_detail_response_model, code=200, description='获取成功')
    @conversation_ns.marshal_with(error_model, code=404, description='对话不存在')
    @conversation_ns.marshal_with(error_model, code=500, description='服务器内部错误')
    @jwt_required()
    def get(self, conversation_id):
        """获取特定对话详情"""
        try:
            user_id = int(get_jwt_identity())
            
            conversation = Conversation.query.filter_by(
                id=conversation_id, user_id=user_id
            ).first()
            
            if not conversation:
                return {'message': '对话不存在'}, 404
            
            return {
                'conversation': conversation.to_dict(include_messages=True)
            }, 200
            
        except Exception as e:
            return {'message': '获取对话详情失败'}, 500

    @conversation_ns.doc('update_conversation', security='Bearer')
    @conversation_ns.expect(conversation_update_model)
    @conversation_ns.marshal_with(conversation_response_model, code=200, description='更新成功')
    @conversation_ns.marshal_with(error_model, code=400, description='请求参数错误')
    @conversation_ns.marshal_with(error_model, code=404, description='对话不存在')
    @conversation_ns.marshal_with(error_model, code=500, description='服务器内部错误')
    @jwt_required()
    def put(self, conversation_id):
        """更新对话信息"""
        try:
            user_id = int(get_jwt_identity())
            data = request.get_json()
            
            if not data:
                return {'message': '缺少更新数据'}, 400
            
            conversation = Conversation.query.filter_by(
                id=conversation_id, user_id=user_id
            ).first()
            
            if not conversation:
                return {'message': '对话不存在'}, 404
            
            # 更新标题
            if 'title' in data:
                title = data['title'].strip()
                if title:
                    conversation.title = title
            
            db.session.commit()
            
            return {
                'message': '对话更新成功',
                'conversation': conversation.to_dict()
            }, 200
            
        except Exception as e:
            db.session.rollback()
            return {'message': '更新对话失败'}, 500

    @conversation_ns.doc('delete_conversation', security='Bearer')
    @conversation_ns.marshal_with(error_model, code=200, description='删除成功')
    @conversation_ns.marshal_with(error_model, code=404, description='对话不存在')
    @conversation_ns.marshal_with(error_model, code=500, description='服务器内部错误')
    @jwt_required()
    def delete(self, conversation_id):
        """删除对话"""
        try:
            user_id = int(get_jwt_identity())
            
            conversation = Conversation.query.filter_by(
                id=conversation_id, user_id=user_id
            ).first()
            
            if not conversation:
                return {'message': '对话不存在'}, 404
            
            db.session.delete(conversation)
            db.session.commit()
            
            return {'message': '对话删除成功'}, 200
            
        except Exception as e:
            db.session.rollback()
            return {'message': '删除对话失败'}, 500

@conversation_ns.route('/<int:conversation_id>/messages')
class MessageList(Resource):
    @conversation_ns.doc('get_messages', security='Bearer')
    @conversation_ns.param('page', '页码', type='integer', default=1)
    @conversation_ns.param('per_page', '每页数量', type='integer', default=50)
    @conversation_ns.marshal_with(messages_response_model, code=200, description='获取成功')
    @conversation_ns.marshal_with(error_model, code=404, description='对话不存在')
    @conversation_ns.marshal_with(error_model, code=500, description='服务器内部错误')
    @jwt_required()
    def get(self, conversation_id):
        """获取对话消息历史"""
        try:
            user_id = int(get_jwt_identity())
            page = request.args.get('page', 1, type=int)
            per_page = request.args.get('per_page', 50, type=int)
            
            # 验证对话所有权
            conversation = Conversation.query.filter_by(
                id=conversation_id, user_id=user_id
            ).first()
            
            if not conversation:
                return {'message': '对话不存在'}, 404
            
            # 限制每页数量
            per_page = min(per_page, 100)
            
            messages = Message.query.filter_by(conversation_id=conversation_id)\
                .order_by(Message.created_at.asc())\
                .paginate(page=page, per_page=per_page, error_out=False)
            
            return {
                'messages': [msg.to_dict() for msg in messages.items],
                'pagination': {
                    'page': page,
                    'per_page': per_page,
                    'total': messages.total,
                    'pages': messages.pages,
                    'has_next': messages.has_next,
                    'has_prev': messages.has_prev
                }
            }, 200
            
        except Exception as e:
            return {'message': '获取消息历史失败'}, 500

    @conversation_ns.doc('send_message', security='Bearer')
    @conversation_ns.expect(message_create_model)
    @conversation_ns.marshal_with(send_message_response_model, code=201, description='发送成功')
    @conversation_ns.marshal_with(send_message_response_model, code=202, description='消息已保存，AI服务暂时不可用')
    @conversation_ns.marshal_with(error_model, code=400, description='请求参数错误')
    @conversation_ns.marshal_with(error_model, code=404, description='对话不存在')
    @conversation_ns.marshal_with(error_model, code=500, description='服务器内部错误')
    @jwt_required()
    def post(self, conversation_id):
        """发送消息并获取AI回复"""
        try:
            user_id = int(get_jwt_identity())
            data = request.get_json()
            
            if not data or 'content' not in data:
                return {'message': '缺少消息内容'}, 400
            
            content = data['content'].strip()
            if not content:
                return {'message': '消息内容不能为空'}, 400
            
            # 验证对话所有权
            conversation = Conversation.query.filter_by(
                id=conversation_id, user_id=user_id
            ).first()
            
            if not conversation:
                return {'message': '对话不存在'}, 404
            
            # 保存用户消息
            user_message = Message(
                conversation_id=conversation_id,
                role='user',
                content=content
            )
            db.session.add(user_message)
            
            # 获取AI回复
            try:
                ai_response = get_ai_response(content, conversation_id)
                
                # 保存AI回复
                ai_message = Message(
                    conversation_id=conversation_id,
                    role='assistant',
                    content=ai_response
                )
                db.session.add(ai_message)
                
                # 更新对话的最后更新时间
                conversation.updated_at = db.func.now()
                
                db.session.commit()
                
                return {
                    'message': '消息发送成功',
                    'user_message': user_message.to_dict(),
                    'ai_message': ai_message.to_dict()
                }, 201
                
            except Exception as ai_error:
                # AI服务失败时，仍保存用户消息
                db.session.commit()
                return {
                    'message': '消息已保存，但AI服务暂时不可用',
                    'user_message': user_message.to_dict(),
                    'ai_message': None,
                    'error': 'AI服务暂时不可用，请稍后重试'
                }, 202
            
        except Exception as e:
            db.session.rollback()
            return {'message': '发送消息失败'}, 500
