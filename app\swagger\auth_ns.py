from flask_restx import Namespace, Resource, fields
from flask import request
from flask_jwt_extended import jwt_required, get_jwt_identity
from sqlalchemy.exc import IntegrityError
from app import db
from app.models.user import User
import re

# 创建认证命名空间
auth_ns = Namespace('auth', description='用户认证相关接口')

# 定义数据模型
user_register_model = auth_ns.model('UserRegister', {
    'username': fields.String(required=True, description='用户名', example='testuser'),
    'email': fields.String(required=True, description='邮箱', example='<EMAIL>'),
    'password': fields.String(required=True, description='密码', example='password123')
})

user_login_model = auth_ns.model('UserLogin', {
    'username': fields.String(required=True, description='用户名或邮箱', example='testuser'),
    'password': fields.String(required=True, description='密码', example='password123')
})

user_info_model = auth_ns.model('UserInfo', {
    'id': fields.Integer(description='用户ID'),
    'username': fields.String(description='用户名'),
    'email': fields.String(description='邮箱'),
    'created_at': fields.String(description='创建时间'),
    'updated_at': fields.String(description='更新时间')
})

auth_response_model = auth_ns.model('AuthResponse', {
    'message': fields.String(description='响应消息'),
    'access_token': fields.String(description='访问令牌'),
    'user': fields.Nested(user_info_model, description='用户信息')
})

profile_response_model = auth_ns.model('ProfileResponse', {
    'user': fields.Nested(user_info_model, description='用户信息')
})

error_model = auth_ns.model('Error', {
    'message': fields.String(description='错误消息')
})

def validate_email(email):
    """验证邮箱格式"""
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return re.match(pattern, email) is not None

def validate_password(password):
    """验证密码强度"""
    if len(password) < 6:
        return False, "密码长度至少6位"
    return True, ""

@auth_ns.route('/register')
class UserRegister(Resource):
    @auth_ns.doc('user_register')
    @auth_ns.expect(user_register_model)
    @auth_ns.marshal_with(auth_response_model, code=201, description='注册成功')
    @auth_ns.marshal_with(error_model, code=400, description='请求参数错误')
    @auth_ns.marshal_with(error_model, code=409, description='用户已存在')
    @auth_ns.marshal_with(error_model, code=500, description='服务器内部错误')
    def post(self):
        """用户注册"""
        try:
            data = request.get_json()
            
            # 验证必填字段
            if not data or not all(k in data for k in ('username', 'email', 'password')):
                return {'message': '缺少必填字段'}, 400
            
            username = data['username'].strip()
            email = data['email'].strip().lower()
            password = data['password']
            
            # 验证输入
            if not username or len(username) < 3:
                return {'message': '用户名长度至少3位'}, 400
            
            if not validate_email(email):
                return {'message': '邮箱格式不正确'}, 400
            
            is_valid, msg = validate_password(password)
            if not is_valid:
                return {'message': msg}, 400
            
            # 检查用户是否已存在
            if User.query.filter_by(username=username).first():
                return {'message': '用户名已存在'}, 409
            
            if User.query.filter_by(email=email).first():
                return {'message': '邮箱已被注册'}, 409
            
            # 创建新用户
            from flask_jwt_extended import create_access_token
            user = User(username=username, email=email, password=password)
            db.session.add(user)
            db.session.commit()
            
            # 生成访问令牌
            access_token = create_access_token(identity=str(user.id))
            
            return {
                'message': '注册成功',
                'access_token': access_token,
                'user': user.to_dict()
            }, 201
            
        except IntegrityError:
            db.session.rollback()
            return {'message': '用户名或邮箱已存在'}, 409
        except Exception as e:
            db.session.rollback()
            return {'message': '注册失败，请稍后重试'}, 500

@auth_ns.route('/login')
class UserLogin(Resource):
    @auth_ns.doc('user_login')
    @auth_ns.expect(user_login_model)
    @auth_ns.marshal_with(auth_response_model, code=200, description='登录成功')
    @auth_ns.marshal_with(error_model, code=400, description='请求参数错误')
    @auth_ns.marshal_with(error_model, code=401, description='用户名或密码错误')
    @auth_ns.marshal_with(error_model, code=500, description='服务器内部错误')
    def post(self):
        """用户登录"""
        try:
            data = request.get_json()
            
            if not data or not all(k in data for k in ('username', 'password')):
                return {'message': '缺少用户名或密码'}, 400
            
            username = data['username'].strip()
            password = data['password']
            
            # 查找用户（支持用户名或邮箱登录）
            user = User.query.filter(
                (User.username == username) | (User.email == username)
            ).first()
            
            if not user or not user.check_password(password):
                return {'message': '用户名或密码错误'}, 401
            
            # 生成访问令牌
            from flask_jwt_extended import create_access_token
            access_token = create_access_token(identity=str(user.id))
            
            return {
                'message': '登录成功',
                'access_token': access_token,
                'user': user.to_dict()
            }, 200
            
        except Exception as e:
            return {'message': '登录失败，请稍后重试'}, 500

@auth_ns.route('/profile')
class UserProfile(Resource):
    @auth_ns.doc('get_user_profile', security='Bearer')
    @auth_ns.marshal_with(profile_response_model, code=200, description='获取成功')
    @auth_ns.marshal_with(error_model, code=401, description='未授权')
    @auth_ns.marshal_with(error_model, code=404, description='用户不存在')
    @auth_ns.marshal_with(error_model, code=500, description='服务器内部错误')
    @jwt_required()
    def get(self):
        """获取用户信息"""
        try:
            user_id = int(get_jwt_identity())
            user = User.query.get(user_id)
            
            if not user:
                return {'message': '用户不存在'}, 404
            
            return {
                'user': user.to_dict()
            }, 200
            
        except Exception as e:
            return {'message': '获取用户信息失败'}, 500

@auth_ns.route('/logout')
class UserLogout(Resource):
    @auth_ns.doc('user_logout', security='Bearer')
    @auth_ns.marshal_with(error_model, code=200, description='登出成功')
    @jwt_required()
    def post(self):
        """用户登出"""
        # 注意：在简单实现中，JWT token无法在服务端主动失效
        # 如需实现真正的登出，需要维护一个token黑名单
        return {'message': '登出成功'}, 200
