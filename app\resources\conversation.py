from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from app import db
from app.models.user import User
from app.models.conversation import Conversation
from app.models.message import Message
from app.utils.ai_service import get_ai_response

conversation_bp = Blueprint('conversation', __name__)

@conversation_bp.route('', methods=['GET'])
@jwt_required()
def get_conversations():
    """获取用户的对话列表"""
    try:
        user_id = int(get_jwt_identity())
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        
        # 限制每页数量
        per_page = min(per_page, 100)
        
        conversations = Conversation.query.filter_by(user_id=user_id)\
            .order_by(Conversation.updated_at.desc())\
            .paginate(page=page, per_page=per_page, error_out=False)
        
        return jsonify({
            'conversations': [conv.to_dict() for conv in conversations.items],
            'pagination': {
                'page': page,
                'per_page': per_page,
                'total': conversations.total,
                'pages': conversations.pages,
                'has_next': conversations.has_next,
                'has_prev': conversations.has_prev
            }
        }), 200
        
    except Exception as e:
        return jsonify({'message': '获取对话列表失败'}), 500

@conversation_bp.route('', methods=['POST'])
@jwt_required()
def create_conversation():
    """创建新对话"""
    try:
        user_id = int(get_jwt_identity())
        data = request.get_json() or {}
        
        title = data.get('title', '新对话').strip()
        if not title:
            title = '新对话'
        
        # 创建对话
        conversation = Conversation(user_id=user_id, title=title)
        db.session.add(conversation)
        db.session.commit()
        
        return jsonify({
            'message': '对话创建成功',
            'conversation': conversation.to_dict()
        }), 201
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'message': '创建对话失败'}), 500

@conversation_bp.route('/<int:conversation_id>', methods=['GET'])
@jwt_required()
def get_conversation(conversation_id):
    """获取特定对话详情"""
    try:
        user_id = int(get_jwt_identity())

        conversation = Conversation.query.filter_by(
            id=conversation_id, user_id=user_id
        ).first()
        
        if not conversation:
            return jsonify({'message': '对话不存在'}), 404
        
        return jsonify({
            'conversation': conversation.to_dict(include_messages=True)
        }), 200
        
    except Exception as e:
        return jsonify({'message': '获取对话详情失败'}), 500

@conversation_bp.route('/<int:conversation_id>', methods=['PUT'])
@jwt_required()
def update_conversation(conversation_id):
    """更新对话信息"""
    try:
        user_id = int(get_jwt_identity())
        data = request.get_json()
        
        if not data:
            return jsonify({'message': '缺少更新数据'}), 400
        
        conversation = Conversation.query.filter_by(
            id=conversation_id, user_id=user_id
        ).first()
        
        if not conversation:
            return jsonify({'message': '对话不存在'}), 404
        
        # 更新标题
        if 'title' in data:
            title = data['title'].strip()
            if title:
                conversation.title = title
        
        db.session.commit()
        
        return jsonify({
            'message': '对话更新成功',
            'conversation': conversation.to_dict()
        }), 200
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'message': '更新对话失败'}), 500

@conversation_bp.route('/<int:conversation_id>', methods=['DELETE'])
@jwt_required()
def delete_conversation(conversation_id):
    """删除对话"""
    try:
        user_id = int(get_jwt_identity())

        conversation = Conversation.query.filter_by(
            id=conversation_id, user_id=user_id
        ).first()
        
        if not conversation:
            return jsonify({'message': '对话不存在'}), 404
        
        db.session.delete(conversation)
        db.session.commit()
        
        return jsonify({'message': '对话删除成功'}), 200
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'message': '删除对话失败'}), 500

@conversation_bp.route('/<int:conversation_id>/messages', methods=['GET'])
@jwt_required()
def get_messages(conversation_id):
    """获取对话消息历史"""
    try:
        user_id = int(get_jwt_identity())
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 50, type=int)

        # 验证对话所有权
        conversation = Conversation.query.filter_by(
            id=conversation_id, user_id=user_id
        ).first()

        if not conversation:
            return jsonify({'message': '对话不存在'}), 404

        # 限制每页数量
        per_page = min(per_page, 100)

        messages = Message.query.filter_by(conversation_id=conversation_id)\
            .order_by(Message.created_at.asc())\
            .paginate(page=page, per_page=per_page, error_out=False)

        return jsonify({
            'messages': [msg.to_dict() for msg in messages.items],
            'pagination': {
                'page': page,
                'per_page': per_page,
                'total': messages.total,
                'pages': messages.pages,
                'has_next': messages.has_next,
                'has_prev': messages.has_prev
            }
        }), 200

    except Exception as e:
        return jsonify({'message': '获取消息历史失败'}), 500

@conversation_bp.route('/<int:conversation_id>/messages', methods=['POST'])
@jwt_required()
def send_message(conversation_id):
    """发送消息并获取AI回复"""
    try:
        user_id = int(get_jwt_identity())
        data = request.get_json()

        if not data or 'content' not in data:
            return jsonify({'message': '缺少消息内容'}), 400

        content = data['content'].strip()
        if not content:
            return jsonify({'message': '消息内容不能为空'}), 400

        # 验证对话所有权
        conversation = Conversation.query.filter_by(
            id=conversation_id, user_id=user_id
        ).first()

        if not conversation:
            return jsonify({'message': '对话不存在'}), 404

        # 保存用户消息
        user_message = Message(
            conversation_id=conversation_id,
            role='user',
            content=content
        )
        db.session.add(user_message)

        # 获取AI回复
        try:
            ai_response = get_ai_response(content, conversation_id)

            # 保存AI回复
            ai_message = Message(
                conversation_id=conversation_id,
                role='assistant',
                content=ai_response
            )
            db.session.add(ai_message)

            # 更新对话的最后更新时间
            conversation.updated_at = db.func.now()

            db.session.commit()

            return jsonify({
                'message': '消息发送成功',
                'user_message': user_message.to_dict(),
                'ai_message': ai_message.to_dict()
            }), 201

        except Exception as ai_error:
            # AI服务失败时，仍保存用户消息
            db.session.commit()
            return jsonify({
                'message': '消息已保存，但AI服务暂时不可用',
                'user_message': user_message.to_dict(),
                'ai_message': None,
                'error': 'AI服务暂时不可用，请稍后重试'
            }), 202

    except Exception as e:
        db.session.rollback()
        return jsonify({'message': '发送消息失败'}), 500
