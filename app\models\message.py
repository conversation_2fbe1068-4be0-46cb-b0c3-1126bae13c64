from datetime import datetime
from app import db

class Message(db.Model):
    """消息模型"""
    __tablename__ = 'messages'
    
    id = db.<PERSON>umn(db.Integer, primary_key=True)
    conversation_id = db.<PERSON>umn(db.<PERSON><PERSON><PERSON>, db.<PERSON><PERSON>('conversations.id'), nullable=False)
    role = db.<PERSON>umn(db.<PERSON>('user', 'assistant', name='message_role'), nullable=False)
    content = db.Column(db.Text, nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    def __init__(self, conversation_id, role, content):
        self.conversation_id = conversation_id
        self.role = role
        self.content = content
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'conversation_id': self.conversation_id,
            'role': self.role,
            'content': self.content,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }
    
    def __repr__(self):
        return f'<Message {self.id}: {self.role}>'
