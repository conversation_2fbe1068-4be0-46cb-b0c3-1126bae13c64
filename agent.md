1.使用 Vue3 + Pinia + Axios 创建对话 UI

2.后端 Flask 提供 /chat 接口，调用 OpenAI

3.定义一些函数接口如 getWeather(city)、searchPDF(keyword)，并注册到 Agent 里

4.使用 Function Calling 的方式让模型选择是否调用

5.用 localStorage 做会话记录 / 模拟记忆

6.添加 Markdown 渲染、图片上传、模型选择器等组件




// 伪代码示例
const messages = [
  { role: 'sy', content: '你是一个医学助手' },
  { role: 'user', content: '肺结节严重吗？' },
  { role: 'ass', content: '肺结节常见于...' },
  { role: 'user', content: '会发展成癌症吗？' },
]
const response = await callLLM(messages)
