# AI医疗小助手后端

基于Flask的RESTful API后端服务，提供用户认证和AI医疗咨询对话功能。

## 功能特性

- 用户注册/登录/认证
- JWT Token认证
- 对话管理（创建、查看、删除）
- 消息发送和AI回复
- 对话历史记录
- MySQL数据库支持
- AI服务集成（支持自定义AI API）

## 技术栈

- **框架**: Flask + Flask-RESTful
- **数据库**: MySQL + SQLAlchemy ORM
- **认证**: JWT (Flask-JWT-Extended)
- **迁移**: Flask-Migrate
- **跨域**: Flask-CORS

## 快速开始

### 1. 环境准备

```bash
# 克隆项目
git clone <repository-url>
cd medical-assistant-backend

# 创建虚拟环境
python -m venv venv

# 激活虚拟环境
# Windows
venv\Scripts\activate
# Linux/Mac
source venv/bin/activate

# 安装依赖
pip install -r requirements.txt
```

### 2. 数据库配置

```bash
# 复制环境变量文件
cp .env.example .env

# 编辑 .env 文件，配置数据库连接信息
# 确保MySQL服务已启动，并创建对应的数据库
```

### 3. 初始化数据库

```bash
# 初始化迁移
flask db init

# 生成迁移文件
flask db migrate -m "Initial migration"

# 执行迁移
flask db upgrade

# 或者直接创建表（简单方式）
flask init-db
```

### 4. 启动应用

```bash
python app.py
```

应用将在 `http://localhost:5000` 启动。

## API文档

### 认证相关

#### 用户注册
```
POST /api/v1/auth/register
Content-Type: application/json

{
    "username": "testuser",
    "email": "<EMAIL>",
    "password": "password123"
}
```

#### 用户登录
```
POST /api/v1/auth/login
Content-Type: application/json

{
    "username": "testuser",
    "password": "password123"
}
```

#### 获取用户信息
```
GET /api/v1/auth/profile
Authorization: Bearer <access_token>
```

### 对话相关

#### 获取对话列表
```
GET /api/v1/conversations?page=1&per_page=20
Authorization: Bearer <access_token>
```

#### 创建新对话
```
POST /api/v1/conversations
Authorization: Bearer <access_token>
Content-Type: application/json

{
    "title": "健康咨询"
}
```

#### 获取对话详情
```
GET /api/v1/conversations/{conversation_id}
Authorization: Bearer <access_token>
```

#### 发送消息
```
POST /api/v1/conversations/{conversation_id}/messages
Authorization: Bearer <access_token>
Content-Type: application/json

{
    "content": "我最近头痛，请问可能是什么原因？"
}
```

#### 获取消息历史
```
GET /api/v1/conversations/{conversation_id}/messages?page=1&per_page=50
Authorization: Bearer <access_token>
```

## 环境变量说明

| 变量名 | 说明 | 默认值 |
|--------|------|--------|
| DATABASE_URL | 数据库连接URL | - |
| MYSQL_HOST | MySQL主机 | localhost |
| MYSQL_PORT | MySQL端口 | 3306 |
| MYSQL_USER | MySQL用户名 | root |
| MYSQL_PASSWORD | MySQL密码 | - |
| MYSQL_DATABASE | 数据库名 | medical_assistant |
| JWT_SECRET_KEY | JWT密钥 | - |
| JWT_ACCESS_TOKEN_EXPIRES | Token过期时间(秒) | 3600 |
| AI_API_KEY | AI服务API密钥 | - |
| AI_API_URL | AI服务API地址 | - |

## 开发说明

### 项目结构
```
├── app/
│   ├── __init__.py          # 应用工厂
│   ├── models/              # 数据模型
│   │   ├── user.py
│   │   ├── conversation.py
│   │   └── message.py
│   ├── resources/           # API资源
│   │   ├── auth.py
│   │   └── conversation.py
│   └── utils/               # 工具模块
│       └── ai_service.py
├── config.py                # 配置文件
├── app.py                   # 应用入口
├── requirements.txt         # 依赖列表
└── README.md               # 说明文档
```

### AI服务集成

项目支持集成外部AI服务，如OpenAI、Claude等。在 `.env` 文件中配置相应的API密钥和地址即可。

如果未配置AI服务，系统将使用内置的模拟回复功能进行演示。

## 部署

### Docker部署（推荐）

```bash
# 构建镜像
docker build -t medical-assistant-backend .

# 运行容器
docker run -d \
  --name medical-assistant \
  -p 5000:5000 \
  --env-file .env \
  medical-assistant-backend
```

### 传统部署

1. 配置生产环境的数据库
2. 设置环境变量 `FLASK_ENV=production`
3. 使用 Gunicorn 等WSGI服务器运行应用

```bash
pip install gunicorn
gunicorn -w 4 -b 0.0.0.0:5000 app:app
```

## 注意事项

1. **安全性**: 生产环境请务必修改默认的密钥
2. **数据库**: 确保MySQL服务正常运行
3. **AI服务**: 根据需要配置相应的AI API
4. **日志**: 生产环境建议配置日志记录
5. **监控**: 建议添加应用监控和健康检查

## 许可证

MIT License
