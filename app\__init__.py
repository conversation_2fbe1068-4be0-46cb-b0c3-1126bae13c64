from flask import Flask
from flask_sqlalchemy import SQLAlchemy
from flask_migrate import Migrate
from flask_jwt_extended import JWTManager
from flask_cors import CORS
from config import config

# 初始化扩展
db = SQLAlchemy()
migrate = Migrate()
jwt = JWTManager()
cors = CORS()

def create_app(config_name='default'):
    """应用工厂函数"""
    app = Flask(__name__)
    
    # 加载配置
    app.config.from_object(config[config_name])
    
    # 初始化扩展
    db.init_app(app)
    migrate.init_app(app, db)
    jwt.init_app(app)
    cors.init_app(app, origins=app.config['CORS_ORIGINS'])
    
    # 注册蓝图
    from app.resources.auth import auth_bp
    from app.resources.conversation import conversation_bp
    from app.swagger import api_bp

    app.register_blueprint(auth_bp, url_prefix='/api/v1/auth')
    app.register_blueprint(conversation_bp, url_prefix='/api/v1/conversations')
    app.register_blueprint(api_bp)  # Swagger API文档
    
    # 注册错误处理器
    @app.errorhandler(404)
    def not_found(error):
        return {'message': '资源未找到'}, 404
    
    @app.errorhandler(500)
    def internal_error(error):
        return {'message': '服务器内部错误'}, 500
    
    # JWT错误处理
    @jwt.expired_token_loader
    def expired_token_callback(jwt_header, jwt_payload):
        return {'message': 'Token已过期'}, 401
    
    @jwt.invalid_token_loader
    def invalid_token_callback(error):
        return {'message': '无效的Token'}, 401
    
    @jwt.unauthorized_loader
    def missing_token_callback(error):
        return {'message': '需要提供Token'}, 401
    
    return app
