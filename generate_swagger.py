#!/usr/bin/env python3
"""
生成Swagger JSON文档
"""

import json
import os
from app import create_app
from app.swagger import api

def generate_swagger_json():
    """生成Swagger JSON文档"""
    app = create_app()

    with app.test_request_context():
        # 获取Swagger规范
        swagger_spec = api.__schema__
        
        # 确保docs目录存在
        docs_dir = 'docs'
        if not os.path.exists(docs_dir):
            os.makedirs(docs_dir)
        
        # 保存为JSON文件
        json_file = os.path.join(docs_dir, 'swagger.json')
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(swagger_spec, f, ensure_ascii=False, indent=2)
        
        print(f"✅ Swagger JSON文档已生成: {json_file}")
        
        # 保存为YAML文件（可选）
        try:
            import yaml
            yaml_file = os.path.join(docs_dir, 'swagger.yaml')
            with open(yaml_file, 'w', encoding='utf-8') as f:
                yaml.dump(swagger_spec, f, default_flow_style=False, allow_unicode=True)
            print(f"✅ Swagger YAML文档已生成: {yaml_file}")
        except ImportError:
            print("⚠️  未安装PyYAML，跳过YAML文件生成")
        
        # 显示访问信息
        print("\n📋 API文档访问信息:")
        print("- Swagger UI: http://127.0.0.1:5000/api/v1/docs/")
        print("- JSON文档: http://127.0.0.1:5000/api/v1/swagger.json")
        print(f"- 本地JSON: {json_file}")
        
        # 显示Apifox导入信息
        print("\n🔗 Apifox导入方法:")
        print("1. 在Apifox中选择 '导入' -> 'URL导入'")
        print("2. 输入URL: http://127.0.0.1:5000/api/v1/swagger.json")
        print("3. 或者直接导入本地文件: docs/swagger.json")
        
        return json_file

if __name__ == "__main__":
    generate_swagger_json()
