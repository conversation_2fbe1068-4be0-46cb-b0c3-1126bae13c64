import requests
import json
from flask import current_app
from app.models.message import Message
from app.models.conversation import Conversation

def get_conversation_history(conversation_id, limit=10):
    """获取对话历史，用于提供上下文"""
    try:
        messages = Message.query.filter_by(conversation_id=conversation_id)\
            .order_by(Message.created_at.desc())\
            .limit(limit)\
            .all()
        
        # 反转顺序，使其按时间正序排列
        messages.reverse()
        
        history = []
        for msg in messages:
            history.append({
                'role': msg.role,
                'content': msg.content
            })
        
        return history
    except Exception as e:
        current_app.logger.error(f"获取对话历史失败: {e}")
        return []

def get_ai_response(user_message, conversation_id=None):
    """
    获取AI回复
    这里提供了一个基础的实现框架，可以根据需要集成不同的AI服务
    """
    try:
        # 获取配置
        ai_api_key = current_app.config.get('AI_API_KEY')
        ai_api_url = current_app.config.get('AI_API_URL')

        current_app.logger.info(f"AI配置检查 - API Key: {'已配置' if ai_api_key else '未配置'}, API URL: {ai_api_url}")

        # 如果没有配置AI服务，返回错误
        if not ai_api_key or not ai_api_url:
            current_app.logger.error("AI服务未配置")
            raise Exception("AI服务未配置，请联系管理员")
        
        # 构建请求数据
        messages = []
        
        # 添加系统提示
        messages.append({
            'role': 'system',
            'content': '''你是一个专业的AI医疗助手。请注意：
1. 你只能提供一般性的健康信息和建议
2. 不能替代专业医生的诊断和治疗
3. 对于严重症状，建议用户及时就医
4. 回答要专业、准确、易懂
5. 如果不确定，请明确说明并建议咨询医生'''
        })
        
        # 添加对话历史（如果有）
        if conversation_id:
            history = get_conversation_history(conversation_id)
            messages.extend(history)
        
        # 添加当前用户消息
        messages.append({
            'role': 'user',
            'content': user_message
        })
        
        # 调用AI API（这里以OpenAI格式为例）
        headers = {
            'Authorization': f'Bearer {ai_api_key}',
            'Content-Type': 'application/json'
        }
        
        # 根据API URL判断使用的模型
        if 'deepseek' in ai_api_url.lower():
            model_name = 'deepseek-chat'
        else:
            model_name = 'gpt-3.5-turbo'

        data = {
            'model': model_name,
            'messages': messages,
            'max_tokens': 1000,
            'temperature': 0.7
        }
        
        current_app.logger.info(f"发送请求到: {ai_api_url}")
        current_app.logger.info(f"使用模型: {model_name}")

        response = requests.post(
            ai_api_url,
            headers=headers,
            json=data,
            timeout=30
        )

        current_app.logger.info(f"API响应状态码: {response.status_code}")

        if response.status_code == 200:
            result = response.json()
            ai_reply = result['choices'][0]['message']['content']
            current_app.logger.info(f"AI回复成功: {ai_reply[:50]}...")
            return ai_reply.strip()
        else:
            current_app.logger.error(f"AI API错误: {response.status_code} - {response.text}")
            raise Exception(f"AI API调用失败: {response.status_code} - {response.text}")
            
    except requests.exceptions.Timeout:
        current_app.logger.error("AI API请求超时")
        return "抱歉，AI服务响应超时，请稍后重试。"
    except requests.exceptions.RequestException as e:
        current_app.logger.error(f"AI API请求失败: {e}")
        raise Exception(f"AI API请求失败: {e}")
    except Exception as e:
        current_app.logger.error(f"AI服务异常: {e}")
        return "抱歉，AI服务暂时不可用，请稍后重试。"


