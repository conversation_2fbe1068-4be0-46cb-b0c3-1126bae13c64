# 数据库配置
DATABASE_URL=mysql+pymysql://username:password@localhost:3306/medical_assistant
MYSQL_HOST=localhost
MYSQL_PORT=3306
MYSQL_USER=root
MYSQL_PASSWORD=your_password
MYSQL_DATABASE=medical_assistant

# JWT配置
JWT_SECRET_KEY=your-super-secret-jwt-key-change-this-in-production
JWT_ACCESS_TOKEN_EXPIRES=3600

# Flask配置
FLASK_ENV=development
FLASK_DEBUG=True
SECRET_KEY=your-super-secret-key-change-this-in-production

# AI服务配置（可选，后续集成时使用）
AI_API_KEY=your-ai-api-key
AI_API_URL=https://api.example.com/v1/chat
